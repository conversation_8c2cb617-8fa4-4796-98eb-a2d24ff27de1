// frontend/src/app/page.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Store, MessageSquare, CreditCard, Star, Users } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON>',
    company: 'Acme Corp',
    quote: 'Market O\'Clock helped us connect with new suppliers and grow our business quickly!'
  },
  {
    name: '<PERSON>',
    company: 'RetailPro',
    quote: 'The platform is easy to use and the social features are a game changer.'
  },
  {
    name: '<PERSON>',
    company: 'BizConnect',
    quote: 'We love the flexibility and the support team is fantastic.'
  }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-background">

      {/* Hero Section */}
      <section className="relative bg-gray-900 text-white min-h-[60vh] flex items-center justify-center overflow-hidden">
        <Image
          src="/images/hero-bg.jpg"
          alt="Marketplace background"
          fill
          className="object-cover opacity-60"
          priority
        />
        <div className="relative z-10 max-w-3xl mx-auto text-center px-4 py-24">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 drop-shadow-lg">Ready to grow your business?</h1>
          <p className="text-xl md:text-2xl text-gray-200 mb-8">Join Market O'Clock today and connect with suppliers and retailers from various industries.</p>
          <Link href="/register">
            <Button size="lg" className="text-lg px-8 py-4">Sign Up Now</Button>
          </Link>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Key Features</h2>
            <p className="text-xl text-gray-600">Discover what makes Market O'Clock the ultimate B2B2C marketplace platform.</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50 shadow hover:shadow-lg transition">
              <div className="p-3 bg-indigo-100 rounded-full mb-4"><Store className="h-8 w-8 text-indigo-600" /></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Marketplace</h3>
              <p className="text-gray-600">Connect suppliers with retailers through our efficient marketplace platform.</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50 shadow hover:shadow-lg transition">
              <div className="p-3 bg-indigo-100 rounded-full mb-4"><MessageSquare className="h-8 w-8 text-indigo-600" /></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Microblogs & Blogs</h3>
              <p className="text-gray-600">Market products through engaging content with social features.</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50 shadow hover:shadow-lg transition">
              <div className="p-3 bg-indigo-100 rounded-full mb-4"><CreditCard className="h-8 w-8 text-indigo-600" /></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Multi-Payment Support</h3>
              <p className="text-gray-600">Secure and flexible payment options for all transactions.</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50 shadow hover:shadow-lg transition">
              <div className="p-3 bg-indigo-100 rounded-full mb-4"><Users className="h-8 w-8 text-indigo-600" /></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Community</h3>
              <p className="text-gray-600">Engage with a vibrant community of businesses and retailers.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8">Featured Products</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { src: 'https://images.unsplash.com/photo-1529374255404-311a2a4f1fd9', title: 'Product 1', desc: 'Amazing product on sale!', sale: '$80.00' },
              { src: 'https://images.unsplash.com/photo-1542272604-787c3835535d', title: 'Product 2', desc: "Don\'t miss this deal!", sale: '$40.00' },
              { src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43', title: 'Product 3', desc: 'Limited time offer!', sale: '$60.00' },
            ].map((product) => (
              <Card key={product.title} className="hover:shadow-2xl transition-shadow">
                <Image src={product.src} alt={product.title} width={400} height={250} className="rounded-t-md object-cover w-full h-48" />
                <CardHeader>
                  <CardTitle>{product.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-2">{product.desc}</p>
                  <p className="text-indigo-600 font-bold text-lg">{product.sale}</p>
                  <Button variant="outline" size="sm" asChild className="mt-4 w-full">
                    <Link href="/marketplace">View Details</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <Button variant="default" size="lg" asChild>
              <Link href="/marketplace">View All Products</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials/Trust Section */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((t) => (
              <div key={t.name} className="bg-gray-50 rounded-lg p-6 shadow hover:shadow-lg transition flex flex-col items-center text-center">
                <Star className="h-8 w-8 text-yellow-400 mb-2" />
                <p className="text-gray-700 italic mb-4">"{t.quote}"</p>
                <div className="font-semibold text-gray-900">{t.name}</div>
                <div className="text-sm text-gray-500">{t.company}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final Call to Action */}
      <section className="py-16 bg-indigo-600">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Join Market O'Clock Today</h2>
          <p className="text-xl text-indigo-100 mb-8">Experience the future of B2B2C commerce and grow your business with us.</p>
          <Link href="/register">
            <Button size="lg" variant="secondary" className="w-full sm:w-auto">Sign Up Now</Button>
          </Link>
        </div>
      </section>
    </div>
  );
}